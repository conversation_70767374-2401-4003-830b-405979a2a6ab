<div align="center">
  <h1>黔进同城</h1>
  <h3>贵州本地生活服务平台</h3>
  
  [![Laravel](https://img.shields.io/badge/Laravel-FF2D20?style=flat-square&logo=laravel&logoColor=white)](https://laravel.com)
  [![Vue.js](https://img.shields.io/badge/Vue.js-35495E?style=flat-square&logo=vue.js&logoColor=4FC08D)](https://vuejs.org/)
  [![MySQL](https://img.shields.io/badge/MySQL-005C84?style=flat-square&logo=mysql&logoColor=white)](https://www.mysql.com/)
  [![Redis](https://img.shields.io/badge/redis-%23DD0031.svg?style=flat-square&logo=redis&logoColor=white)](https://redis.io/)
  
  [项目介绍](#-项目介绍) | [功能特性](#-功能特性) | [技术栈](#-技术栈) | [快速开始](#-快速开始) | [部署指南](#-部署说明) | [文档](#-文档)
</div>

## 📌 项目介绍

黔进同城是一个专注于贵州省的区域性生活服务平台，致力于为贵州本地居民和游客提供全方位的本地生活服务。平台整合了本地资讯、生活服务、文旅活动、美食推荐、旅游攻略、文创产品等内容，同时提供用户可发布的同城信息交流功能。

### 多端支持
- **H5端**：移动端网页版，适配各种移动设备
- **PC端**：桌面版网站，提供完整功能体验
- **微信小程序**：<span style="color: #999;">（开发中）</span> 轻量级应用，即用即走
- **APP**：<span style="color: #999;">（规划中）</span> 原生应用，提供最佳性能体验

## 🎯 核心价值

- **本地化服务**：深耕贵州市场，提供精准的本地生活服务
- **信息聚合**：整合各类生活服务信息，打造一站式服务平台
- **社区互动**：构建本地化社区，促进用户交流与互动
- **文旅融合**：结合贵州特色文化，推广本地旅游资源

## 📋 功能概览

| 功能模块 | 主要功能点 |
|---------|-----------|
| 📰 本地资讯 | 贵州新闻、政策解读、民生热点、城市动态 |
| 🍜 美食推荐 | 特色餐馆、美食攻略、用户点评、优惠信息 |
| 🏞️ 文旅服务 | 景点推荐、旅游攻略、文化体验、特色民宿 |
| 🎨 文创市集 | 贵州手工艺品、非遗文化产品、创意设计、文创活动 |
| 🛒 在线商城 | 文创商品、农特产品、同城配送、全国包邮 |
| 📅 赶集日历 | 各地赶集日期查询、赶集特色商品、赶集提醒、导航服务 |
| 🏠 房产服务 | 房屋租售、新房/二手房、VR看房 |
| 💼 招聘求职 | 全职/兼职、企业认证、简历管理 |
| 🛍️ 二手交易 | 数码、家具、服装等闲置交易 |
| 🛠️ 生活服务 | 家政、维修、教育、婚庆等服务 |
| 🚗 出行服务 | 拼车、顺风车、包车服务 |
| 🔍 寻人寻物 | 寻人启事、失物招领、宠物寻找 |
| 🏬 本地商圈 | 商家入驻、优惠活动、用户评价 |
| 🎉 社区活动 | 同城活动、活动报名、活动回顾 |
| ℹ️ 便民信息 | 停水停电、公交查询、政务信息 |
| 👥 社区互动 | 兴趣小组、同城交友、技能交换 |

## 🚀 技术栈

### 后端技术
- **框架**：Laravel 10.x
- **数据库**：MySQL 8.0
- **缓存**：Redis
- **搜索**：Laravel Scout + MeiliSearch
- **文件存储**：腾讯云轻量对象存储
- **支付集成**：微信支付、支付宝
- **消息队列**：Laravel Horizon + Redis Queue
- **实时通信**：Laravel Echo + WebSocket

### 前端技术
- **框架**：Vue.js 3 + Element Plus
- **构建工具**：Vite
- **状态管理**：Pinia
- **路由**：Vue Router
- **地图服务**：高德地图/腾讯地图
- **多端适配**：
  - 响应式布局（H5/PC）
  - 条件编译（小程序适配）
  - API 统一封装（多端兼容）

## 🛠️ 环境要求

- PHP >= 8.1
- Composer 2.x
- Node.js >= 16.0 & NPM >= 8.0
- MySQL >= 8.0
- Redis >= 6.0

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone [项目仓库地址]
cd qianjin-city
```

### 2. 安装依赖
```bash
composer install
npm install
```

### 3. 配置环境
```bash
cp .env.example .env
php artisan key:generate
```

### 4. 配置数据库
编辑 `.env` 文件，配置数据库连接信息。

### 5. 初始化项目
```bash
php artisan migrate --seed
php artisan storage:link
npm run dev
```

### 6. 启动开发服务器
```bash
php artisan serve
```

## 📦 项目结构

```
qianjin-city/
├── app/                  # 应用核心代码
│   ├── Http/            # 控制器和中间件
│   ├── Models/          # 数据模型
│   ├── Services/        # 业务逻辑
│   └── Repositories/    # 数据访问层
├── config/              # 配置文件
├── database/            # 数据库迁移和种子
├── public/              # 入口文件和静态资源
├── resources/           # 视图和前端资源
│   ├── js/             # Vue组件
│   └── scss/           # 样式文件
├── routes/              # 路由定义
└── tests/               # 测试代码
```

## 📚 文档

- [API 文档](#)（生成后更新链接）
- [部署指南](#-部署说明)
- [开发规范](./docs/DEVELOPMENT.md)
- [贡献指南](./docs/CONTRIBUTING.md)

## 🚀 部署说明

### 生产环境要求
- Linux 服务器 (推荐 Ubuntu 20.04+/CentOS 8+)
- Nginx/Apache
- PHP 8.1+
- MySQL 8.0+
- Redis 6.0+
- Node.js 16+
- Supervisord (管理队列进程)

### 部署步骤
1. 克隆代码到服务器
2. 安装依赖：`composer install --optimize-autoloader --no-dev`
3. 编译前端资源：`npm install && npm run build`
4. 配置环境变量：`cp .env.example .env`
5. 生成应用密钥：`php artisan key:generate`
6. 运行数据库迁移：`php artisan migrate --force`
7. 配置队列处理器（Supervisord）
8. 配置定时任务
9. 配置 Web 服务器（Nginx/Apache）

## 🤝 贡献指南

欢迎提交 Pull Request，请遵循以下步骤：

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 开源协议

本项目采用 [MIT 许可证](LICENSE).

## 📞 联系我们

- 项目负责人：[您的名字]
- 邮箱：[您的邮箱]
- 项目地址：[项目仓库地址]

## 🙏 致谢

感谢以下开源项目：
- [Laravel](https://laravel.com)
- [Vue.js](https://vuejs.org/)
- [Element Plus](https://element-plus.org/)
- 以及所有贡献者！

---

<p align="center">Made with ❤️ in Guizhou, China</p>
