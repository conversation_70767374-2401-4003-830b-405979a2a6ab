<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => '本地资讯',
                'slug' => 'local-news',
                'description' => '贵州本地新闻资讯',
                'icon' => 'el-icon-news',
                'color' => '#409EFF',
                'sort' => 1,
                'is_active' => true,
            ],
            [
                'name' => '美食推荐',
                'slug' => 'food',
                'description' => '贵州特色美食推荐',
                'icon' => 'el-icon-food',
                'color' => '#67C23A',
                'sort' => 2,
                'is_active' => true,
            ],
            [
                'name' => '旅游服务',
                'slug' => 'tourism',
                'description' => '贵州旅游景点和服务',
                'icon' => 'el-icon-map-location',
                'color' => '#E6A23C',
                'sort' => 3,
                'is_active' => true,
            ],
            [
                'name' => '房产信息',
                'slug' => 'real-estate',
                'description' => '房屋买卖租赁信息',
                'icon' => 'el-icon-house',
                'color' => '#F56C6C',
                'sort' => 4,
                'is_active' => true,
            ],
            [
                'name' => '求职招聘',
                'slug' => 'jobs',
                'description' => '本地求职招聘信息',
                'icon' => 'el-icon-user',
                'color' => '#909399',
                'sort' => 5,
                'is_active' => true,
            ],
            [
                'name' => '二手交易',
                'slug' => 'secondhand',
                'description' => '二手物品交易',
                'icon' => 'el-icon-goods',
                'color' => '#606266',
                'sort' => 6,
                'is_active' => true,
            ],
            [
                'name' => '社区活动',
                'slug' => 'community',
                'description' => '社区活动和交流',
                'icon' => 'el-icon-chat-dot-round',
                'color' => '#722ED1',
                'sort' => 7,
                'is_active' => true,
            ],
            [
                'name' => '生活服务',
                'slug' => 'services',
                'description' => '各类生活服务信息',
                'icon' => 'el-icon-service',
                'color' => '#13C2C2',
                'sort' => 8,
                'is_active' => true,
            ]
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
