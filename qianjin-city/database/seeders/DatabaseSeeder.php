<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // 创建管理员用户
        User::factory()->create([
            'name' => '黔进同城管理员',
            'email' => '<EMAIL>',
        ]);

        // 运行其他种子文件
        $this->call([
            CategorySeeder::class,
            ArticleSeeder::class,
        ]);
    }
}
