<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // 创建管理员用户
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => '系统[',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123456'),
                'is_admin' => true,
                'email_verified_at' => now(),
            ]
        );

        // 创建测试普通用户
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => '测试用户',
                'email' => '<EMAIL>',
                'password' => Hash::make('user123456'),
                'is_admin' => false,
                'email_verified_at' => now(),
            ]
        );

        $this->command->info('管理员用户和测试用户创建成功！');
        $this->command->info('管理员账号: <EMAIL> / admin123456');
        $this->command->info('普通用户账::::::: <EMAIL> / user123456');
    }
}
