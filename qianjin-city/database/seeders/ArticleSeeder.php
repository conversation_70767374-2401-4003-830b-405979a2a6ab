<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Article;
use App\Models\Category;
use App\Models\User;

class ArticleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $user = User::first();
        $categories = Category::all();

        $articles = [
            [
                'title' => '贵阳花果园美食街新开张！汇聚全省特色小吃',
                'slug' => 'huaguoyuan-food-street-opening',
                'excerpt' => '花果园美食街正式开业，汇聚了贵州各地特色小吃，让您在贵阳就能品尝到全省美食。',
                'content' => '<p>花果园美食街经过半年的精心筹备，今日正式开业！这里汇聚了来自贵州各地的特色小吃，包括遵义羊肉粉、兴义羊肉粉、贵阳丝娃娃、青岩猪脚等。</p><p>美食街位于花果园购物中心B区，总面积达3000平方米，共有80多家特色小吃店铺。开业期间，所有店铺都有优惠活动。</p>',
                'featured_image' => '/images/huaguoyuan-food.jpg',
                'category_id' => $categories->where('slug', 'food')->first()->id,
                'is_featured' => true,
                'is_top' => true,
                'status' => 'published',
                'published_at' => now(),
            ],
            [
                'title' => '黄果树瀑布景区推出夏季特惠门票',
                'slug' => 'huangguoshu-summer-discount',
                'excerpt' => '黄果树瀑布景区为迎接夏季旅游高峰，推出特惠门票活动，学生和老人可享受半价优惠。',
                'content' => '<p>黄果树瀑布景区宣布，从6月1日起至8月31日，推出夏季特惠门票活动。</p><p>活动期间，学生凭学生证可享受半价优惠，60岁以上老人凭身份证也可享受半价优惠。此外，团体游客（20人以上）可享受8折优惠。</p>',
                'featured_image' => '/images/huangguoshu-waterfall.jpg',
                'category_id' => $categories->where('slug', 'tourism')->first()->id,
                'is_featured' => true,
                'status' => 'published',
                'published_at' => now()->subDays(1),
            ],
            [
                'title' => '贵阳观山湖区新盘开售，均价8000元/平方米',
                'slug' => 'guanshanhu-new-property',
                'excerpt' => '观山湖区核心地段新楼盘正式开售，主推90-120平方米户型，配套设施完善。',
                'content' => '<p>位于观山湖区核心地段的"湖心雅苑"项目今日正式开售。该项目总建筑面积15万平方米，主推90-120平方米的三室两厅户型。</p><p>项目周边配套设施完善，距离地铁2号线仅500米，步行即可到达大型购物中心。小区内设有幼儿园、健身房、游泳池等配套设施。</p>',
                'featured_image' => '/images/guanshanhu-property.jpg',
                'category_id' => $categories->where('slug', 'real-estate')->first()->id,
                'status' => 'published',
                'published_at' => now()->subDays(2),
            ],
            [
                'title' => '贵州大学招聘软件工程师，月薪12K-18K',
                'slug' => 'guizhou-university-job',
                'excerpt' => '贵州大学信息技术中心招聘软件工程师，要求本科以上学历，熟悉Java或Python开发。',
                'content' => '<p>贵州大学信息技术中心因业务发展需要，现招聘软件工程师2名。</p><p>岗位要求：本科以上学历，计算机相关专业；熟悉Java或Python开发；有3年以上开发经验者优先。薪资待遇：12K-18K/月，五险一金，年终奖金。</p>',
                'featured_image' => '/images/job-hiring.jpg',
                'category_id' => $categories->where('slug', 'jobs')->first()->id,
                'status' => 'published',
                'published_at' => now()->subDays(3),
            ],
            [
                'title' => '社区端午节包粽子活动邀请您参加',
                'slug' => 'community-dragon-boat-festival',
                'excerpt' => '花溪社区将举办端午节包粽子活动，邀请社区居民参加，共度传统佳节。',
                'content' => '<p>花溪社区定于6月22日（端午节）上午9点在社区活动中心举办包粽子活动。</p><p>活动现场将准备糯米、红枣、咸蛋黄等材料，邀请社区大妈现场教学包粽子技巧。参与活动的居民可以带走自己包的粽子，现场还有精美小礼品赠送。</p>',
                'featured_image' => '/images/community-activity.jpg',
                'category_id' => $categories->where('slug', 'community')->first()->id,
                'status' => 'published',
                'published_at' => now()->subDays(4),
            ]
        ];

        foreach ($articles as $articleData) {
            $article = Article::create(array_merge($articleData, [
                'user_id' => $user->id,
                'view_count' => rand(100, 1000),
                'like_count' => rand(10, 100),
                'comment_count' => rand(5, 50),
            ]));
        }
    }
}
