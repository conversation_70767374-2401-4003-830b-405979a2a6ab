<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\ArticleController;
use App\Http\Controllers\Api\HomeController;
use App\Http\Controllers\Auth\AuthController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// 首页API
Route::get('/home', [HomeController::class, 'index']);

// 用户认证API
Route::prefix('auth')->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/logout', [AuthController::class, 'logout'])->middleware('auth:sanctum');
    Route::get('/user', [AuthController::class, 'user'])->middleware('auth:sanctum');
});

// 分类相关API
Route::apiResource('categories', CategoryController::class);
Route::get('categories/{category}/articles', [CategoryController::class, 'articles']);
Route::get('categories/{category}/products', [CategoryController::class, 'products']);

// 文章相关API
Route::apiResource('articles', ArticleController::class);
Route::get('articles/featured', [ArticleController::class, 'featured']);
Route::get('articles/popular', [ArticleController::class, 'popular']);
Route::post('articles/{article}/view', [ArticleController::class, 'incrementViews']);

// 产品相关API（待实现）
// Route::apiResource('products', ProductController::class);

// 搜索API（待实现）
// Route::get('search', [SearchController::class, 'index']);

// 测试API
Route::get('test', function () {
    try {
        // 测试数据库连接
        $dbStatus = \Illuminate\Support\Facades\DB::connection()->getPdo() ? 'Connected' : 'Not Connected';
        
        // 测试数据
        $categoriesCount = \App\Models\Category::count();
        $articlesCount = \App\Models\Article::count();
        
        return response()->json([
            'status' => 'success',
            'database' => $dbStatus,
            'data' => [
                'categories_count' => $categoriesCount,
                'articles_count' => $articlesCount,
            ],
            'timestamp' => now(),
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => $e->getMessage(),
            'timestamp' => now(),
        ], 500);
    }
});
