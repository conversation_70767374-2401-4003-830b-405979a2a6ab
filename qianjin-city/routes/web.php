<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\AuthController;

// 认证路由
Route::prefix('auth')->group(function () {
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
    Route::post('/login', [AuthController::class, 'login'])->name('auth.login');
    Route::post('/register', [AuthController::class, 'register'])->name('auth.register');
    Route::post('/logout', [AuthController::class, 'logout'])->name('auth.logout');
});

// 管理后台认证路由
Route::prefix('admin')->group(function () {
    Route::get('/login', [AuthController::class, 'showAdminLogin'])->name('admin.login');
    Route::post('/login', [AuthController::class, 'adminLogin'])->name('admin.auth.login');
});

// API测试路由
Route::get('/test', function () {
    try {
        // 测试数据库连接
        $dbStatus = \Illuminate\Support\Facades\DB::connection()->getPdo() ? 'Connected' : 'Not Connected';
        
        // 获取数据统计
        $stats = [
            'categories' => \App\Models\Category::count(),
            'articles' => \App\Models\Article::count(),
            'users' => \App\Models\User::count(),
        ];
        
        return response()->json([
            'status' => 'success',
            'database' => $dbStatus,
            'stats' => $stats,
            'timestamp' => now()->toDateTimeString(),
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => $e->getMessage(),
        ], 500);
    }
});

// SPA路由 - 所有请求都返回Vue.js
Route::get('/{any}', function () {
    return view('app');
})->where('any', '.*');
