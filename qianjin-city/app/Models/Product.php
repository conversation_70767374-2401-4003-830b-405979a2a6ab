<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'category_id',
        'name',
        'slug',
        'description',
        'content',
        'sku',
        'price',
        'market_price',
        'stock',
        'min_stock',
        'images',
        'attributes',
        'status',
        'is_featured',
        'is_digital',
        'weight',
        'origin',
        'view_count',
        'sale_count',
        'rating',
        'rating_count',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'market_price' => 'decimal:2',
        'weight' => 'decimal:2',
        'rating' => 'decimal:2',
        'stock' => 'integer',
        'min_stock' => 'integer',
        'view_count' => 'integer',
        'sale_count' => 'integer',
        'rating_count' => 'integer',
        'images' => 'array',
        'attributes' => 'array',
        'is_featured' => 'boolean',
        'is_digital' => 'boolean',
    ];

    /**
     * 商品所属用户（商家）
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 商品分类
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * 获取激活状态的商品
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    /**
     * 获取推荐商品
     */
    public function scopeFeatured(Builder $query): Builder
    {
        return $query->where('is_featured', true);
    }

    /**
     * 获取有库存的商品
     */
    public function scopeInStock(Builder $query): Builder
    {
        return $query->where('stock', '>', 0);
    }

    /**
     * 按价格排序
     */
    public function scopeOrderByPrice(Builder $query, string $direction = 'asc'): Builder
    {
        return $query->orderBy('price', $direction);
    }

    /**
     * 按销量排序
     */
    public function scopeOrderBySales(Builder $query): Builder
    {
        return $query->orderBy('sale_count', 'desc');
    }

    /**
     * 按评分排序
     */
    public function scopeOrderByRating(Builder $query): Builder
    {
        return $query->orderBy('rating', 'desc');
    }

    /**
     * 检查是否有库存
     */
    public function hasStock(int $quantity = 1): bool
    {
        return $this->stock >= $quantity;
    }

    /**
     * 检查是否库存不足
     */
    public function isLowStock(): bool
    {
        return $this->stock <= $this->min_stock;
    }

    /**
     * 获取折扣百分比
     */
    public function getDiscountPercentageAttribute(): ?float
    {
        if ($this->market_price && $this->market_price > $this->price) {
            return round((($this->market_price - $this->price) / $this->market_price) * 100, 1);
        }

        return null;
    }

    /**
     * 获取主图片
     */
    public function getFeaturedImageAttribute(): ?string
    {
        return $this->images[0] ?? null;
    }

    /**
     * 增加浏览量
     */
    public function incrementViewCount(): void
    {
        $this->increment('view_count');
    }
}
