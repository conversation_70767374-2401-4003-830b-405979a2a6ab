<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Article;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ArticleController extends Controller
{
    /**
     * 获取文章列表
     */
    public function index(Request $request): JsonResponse
    {
        $query = Article::published()
            ->with(['user:id,name', 'category:id,name,slug,color'])
            ->select([
                'id', 'user_id', 'category_id', 'title', 'slug',
                'excerpt', 'featured_image', 'is_featured', 'is_top',
                'view_count', 'like_count', 'comment_count', 'published_at'
            ]);

        // 分类筛选
        if ($request->filled('category')) {
            $query->whereHas('category', function ($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        // 推荐文章
        if ($request->boolean('featured')) {
            $query->featured();
        }

        // 置顶文章
        if ($request->boolean('top')) {
            $query->top();
        }

        // 排序
        $sortBy = $request->input('sort', 'latest');
        switch ($sortBy) {
            case 'popular':
                $query->orderBy('view_count', 'desc');
                break;
            case 'liked':
                $query->orderBy('like_count', 'desc');
                break;
            case 'commented':
                $query->orderBy('comment_count', 'desc');
                break;
            default:
                $query->latest();
        }

        $articles = $query->paginate(15);

        return response()->json([
            'status' => 'success',
            'data' => $articles
        ]);
    }

    /**
     * 获取文章详情
     */
    public function show(string $slug): JsonResponse
    {
        $article = Article::where('slug', $slug)
            ->published()
            ->with([
                'user:id,name,avatar',
                'category:id,name,slug,color'
            ])
            ->firstOrFail();

        // 增加浏览量
        $article->incrementViewCount();

        // 获取相关文章
        $relatedArticles = Article::published()
            ->where('category_id', $article->category_id)
            ->where('id', '!=', $article->id)
            ->select(['id', 'title', 'slug', 'featured_image', 'published_at'])
            ->latest()
            ->limit(5)
            ->get();

        return response()->json([
            'status' => 'success',
            'data' => [
                'article' => $article,
                'related_articles' => $relatedArticles
            ]
        ]);
    }

    /**
     * 获取推荐文章
     */
    public function featured(): JsonResponse
    {
        $articles = Article::published()
            ->featured()
            ->with(['category:id,name,slug,color'])
            ->select([
                'id', 'category_id', 'title', 'slug', 'excerpt',
                'featured_image', 'view_count', 'published_at'
            ])
            ->latest()
            ->limit(10)
            ->get();

        return response()->json([
            'status' => 'success',
            'data' => $articles
        ]);
    }

    /**
     * 获取热门文章
     */
    public function popular(): JsonResponse
    {
        $articles = Article::published()
            ->with(['category:id,name,slug,color'])
            ->select([
                'id', 'category_id', 'title', 'slug', 'excerpt',
                'featured_image', 'view_count', 'like_count', 'published_at'
            ])
            ->orderBy('view_count', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'status' => 'success',
            'data' => $articles
        ]);
    }
}
