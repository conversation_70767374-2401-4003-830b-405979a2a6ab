<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\JsonResponse;

class CategoryController extends Controller
{
    /**
     * 获取所有激活的分类
     */
    public function index(): JsonResponse
    {
        $categories = Category::active()
            ->select(['id', 'name', 'slug', 'description', 'icon', 'color'])
            ->get();

        return response()->json([
            'status' => 'success',
            'data' => $categories
        ]);
    }

    /**
     * 获取分类详情
     */
    public function show(string $slug): JsonResponse
    {
        $category = Category::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        return response()->json([
            'status' => 'success',
            'data' => $category
        ]);
    }

    /**
     * 获取分类下的文章
     */
    public function articles(string $slug): JsonResponse
    {
        $category = Category::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        $articles = $category->publishedArticles()
            ->with(['user:id,name', 'category:id,name,slug'])
            ->select([
                'id', 'user_id', 'category_id', 'title', 'slug', 
                'excerpt', 'featured_image', 'is_featured', 'is_top',
                'view_count', 'like_count', 'published_at'
            ])
            ->latest()
            ->paginate(15);

        return response()->json([
            'status' => 'success',
            'data' => [
                'category' => $category,
                'articles' => $articles
            ]
        ]);
    }

    /**
     * 获取分类下的商品
     */
    public function products(string $slug): JsonResponse
    {
        $category = Category::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        $products = $category->activeProducts()
            ->with(['user:id,name', 'category:id,name,slug'])
            ->select([
                'id', 'user_id', 'category_id', 'name', 'slug',
                'description', 'price', 'market_price', 'images',
                'is_featured', 'stock', 'sale_count', 'rating'
            ])
            ->paginate(20);

        return response()->json([
            'status' => 'success',
            'data' => [
                'category' => $category,
                'products' => $products
            ]
        ]);
    }
}
