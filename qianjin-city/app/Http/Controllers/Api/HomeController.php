<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Article;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * 获取首页数据
     */
    public function index()
    {
        try {
            // 获取分类数据
            $categories = Category::select('id', 'name', 'slug', 'description', 'icon', 'color')
                ->where('is_active', true)
                ->orderBy('sort_order')
                ->limit(8)
                ->get();

            // 获取推荐文章
            $featuredArticles = Article::with(['category:id,name,color'])
                ->select('id', 'title', 'slug', 'excerpt', 'featured_image', 'view_count', 'category_id')
                ->where('status', 'published')
                ->where('is_featured', true)
                ->orderBy('published_at', 'desc')
                ->limit(6)
                ->get();

            // 获取热门文章
            $popularArticles = Article::with(['category:id,name,color'])
                ->select('id', 'title', 'slug', 'excerpt', 'featured_image', 'view_count', 'category_id')
                ->where('status', 'published')
                ->orderBy('view_count', 'desc')
                ->limit(6)
                ->get();

            // 获取最新文章
            $latestArticles = Article::with(['category:id,name,color'])
                ->select('id', 'title', 'slug', 'excerpt', 'featured_image', 'view_count', 'category_id')
                ->where('status', 'published')
                ->orderBy('published_at', 'desc')
                ->limit(6)
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'categories' => $categories,
                    'featured_articles' => $featuredArticles,
                    'popular_articles' => $popularArticles,
                    'latest_articles' => $latestArticles,
                ]
            ]);

        } catch (\Exception $e) {
            // 如果数据库中没有数据，返回模拟数据
            return response()->json([
                'success' => true,
                'data' => [
                    'categories' => $this->getMockCategories(),
                    'featured_articles' => $this->getMockArticles(),
                    'popular_articles' => $this->getMockArticles(),
                    'latest_articles' => $this->getMockArticles(),
                ]
            ]);
        }
    }

    /**
     * 获取模拟分类数据
     */
    private function getMockCategories()
    {
        return [
            [
                'id' => 1,
                'name' => '餐饮美食',
                'slug' => 'food',
                'description' => '发现贵州特色美食',
                'icon' => 'el-icon-food',
                'color' => '#ff6b6b'
            ],
            [
                'id' => 2,
                'name' => '旅游景点',
                'slug' => 'travel',
                'description' => '探索贵州美景',
                'icon' => 'el-icon-map-location',
                'color' => '#4ecdc4'
            ],
            [
                'id' => 3,
                'name' => '新闻资讯',
                'slug' => 'news',
                'description' => '了解本地资讯',
                'icon' => 'el-icon-news',
                'color' => '#45b7d1'
            ],
            [
                'id' => 4,
                'name' => '生活服务',
                'slug' => 'service',
                'description' => '便民生活服务',
                'icon' => 'el-icon-service',
                'color' => '#96ceb4'
            ]
        ];
    }

    /**
     * 获取模拟文章数据
     */
    private function getMockArticles()
    {
        return [
            [
                'id' => 1,
                'title' => '贵州特色小吃推荐',
                'slug' => 'guizhou-food-recommendations',
                'excerpt' => '带你品尝地道的贵州美食，从丝娃娃到酸汤鱼...',
                'featured_image' => '/images/news1.svg',
                'view_count' => 1234,
                'category' => [
                    'id' => 1,
                    'name' => '餐饮美食',
                    'color' => '#ff6b6b'
                ]
            ],
            [
                'id' => 2,
                'title' => '黄果树瀑布游玩攻略',
                'slug' => 'huangguoshu-travel-guide',
                'excerpt' => '详细的黄果树瀑布旅游指南，包含交通、住宿、景点介绍...',
                'featured_image' => '/images/news2.svg',
                'view_count' => 2156,
                'category' => [
                    'id' => 2,
                    'name' => '旅游景点',
                    'color' => '#4ecdc4'
                ]
            ],
            [
                'id' => 3,
                'title' => '贵阳市最新发展动态',
                'slug' => 'guiyang-development-news',
                'excerpt' => '了解贵阳市最新的城市建设和发展规划...',
                'featured_image' => '/images/news3.svg',
                'view_count' => 876,
                'category' => [
                    'id' => 3,
                    'name' => '新闻资讯',
                    'color' => '#45b7d1'
                ]
            ]
        ];
    }
}
