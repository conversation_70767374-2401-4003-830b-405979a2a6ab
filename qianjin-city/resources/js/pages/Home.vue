<template>
  <div class="home">
    <!-- 顶部导航 -->
    <el-header class="header">
      <div class="header-container">
        <div class="logo">
          <h1>黔进同城</h1>
          <span>贵州本地生活服务平台</span>
        </div>
        <div class="nav-content">
          <el-menu
            mode="horizontal"
            :default-active="activeIndex"
            class="el-menu-demo"
            @select="handleSelect"
          >
            <el-menu-item index="1">首页</el-menu-item>
            <el-menu-item 
              v-for="category in categories" 
              :key="category.id" 
              :index="category.id.toString()"
            >
              {{ category.name }}
            </el-menu-item>
          </el-menu>
          <div class="auth-buttons">
            <template v-if="!user">
              <el-button type="primary" size="small" @click="goToLogin">登录</el-button>
              <el-button type="success" size="small" @click="goToRegister">注册</el-button>
            </template>
            <template v-else>
              <el-dropdown @command="handleUserCommand">
                <span class="user-info">
                  <el-icon><User /></el-icon>
                  {{ user.name }}
                  <el-icon><ArrowDown /></el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                    <el-dropdown-item command="admin" v-if="user.is_admin">管理后台</el-dropdown-item>
                    <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </div>
        </div>
      </div>
    </el-header>

    <!-- 轮播图 -->
    <el-carousel :interval="4000" type="card" height="400px" v-loading="loading">
      <el-carousel-item v-for="item in featuredArticles.slice(0, 3)" :key="item.id">
        <div class="carousel-item" :style="{ backgroundImage: `url(${item.featured_image || '/images/default-banner.svg'})` }">
          <div class="carousel-content">
            <h3>{{ item.title }}</h3>
            <p>{{ item.excerpt }}</p>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>

    <!-- 功能模块 -->
    <div class="features">
      <div class="container">
        <h2>服务分类</h2>
        <el-row :gutter="20">
          <el-col :span="6" v-for="category in categories" :key="category.id">
            <el-card class="feature-card" :body-style="{ padding: '20px' }" @click="goToCategory(category.slug)">
              <div class="feature-icon">
                <el-icon :size="40" :color="category.color">
                  <component :is="getIconComponent(category.icon)" />
                </el-icon>
              </div>
              <h3>{{ category.name }}</h3>
              <p>{{ category.description }}</p>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 推荐文章 -->
    <div class="featured-articles">
      <div class="container">
        <h2>推荐内容</h2>
        <el-row :gutter="20">
          <el-col :span="8" v-for="article in featuredArticles" :key="article.id">
            <el-card class="article-card" @click="goToArticle(article.slug)">
              <img :src="article.featured_image || '/images/default-article.svg'" class="article-image" />
              <div style="padding: 14px;">
                <span class="article-title">{{ article.title }}</span>
                <div class="article-meta">
                  <span class="category" :style="{ color: article.category?.color }">
                    {{ article.category?.name }}
                  </span>
                  <span class="views">{{ article.view_count }} 浏览</span>
                </div>
                <p class="article-excerpt">{{ article.excerpt }}</p>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 最新文章 -->
    <div class="latest-articles">
      <div class="container">
        <h2>最新资讯</h2>
        <el-row :gutter="20">
          <el-col :span="8" v-for="article in latestArticles" :key="article.id">
            <el-card class="article-card" @click="goToArticle(article.slug)">
              <img :src="article.featured_image || '/images/default-article.svg'" class="article-image" />
              <div style="padding: 14px;">
                <span class="article-title">{{ article.title }}</span>
                <div class="article-meta">
                  <span class="category" :style="{ color: article.category?.color }">
                    {{ article.category?.name }}
                  </span>
                  <span class="views">{{ article.view_count }} 浏览</span>
                </div>
                <p class="article-excerpt">{{ article.excerpt }}</p>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 页脚 -->
    <el-footer class="footer">
      <div class="container">
        <p>&copy; 2024 黔进同城. Made with ❤️ in Guizhou, China</p>
      </div>
    </el-footer>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import axios from 'axios';
import {
  House,
  Food,
  Camera,
  Reading,
  User,
  Goods,
  ChatDotRound,
  Tools,
  ArrowDown
} from '@element-plus/icons-vue';

const router = useRouter();
const activeIndex = ref('1');
const categories = ref([]);
const featuredArticles = ref([]);
const popularArticles = ref([]);
const latestArticles = ref([]);
const loading = ref(false);
const user = ref(null);

// 模拟数据（如果API不可用）
const mockCategories = [
  { id: 1, name: '餐饮美食', slug: 'food', icon: 'el-icon-food', color: '#ff6b6b', description: '发现贵州特色美食' },
  { id: 2, name: '旅游景点', slug: 'travel', icon: 'el-icon-map-location', color: '#4ecdc4', description: '探索贵州美景' },
  { id: 3, name: '新闻资讯', slug: 'news', icon: 'el-icon-news', color: '#45b7d1', description: '了解本地资讯' },
  { id: 4, name: '生活服务', slug: 'service', icon: 'el-icon-service', color: '#96ceb4', description: '便民生活服务' }
];

const mockArticles = [
  {
    id: 1,
    title: '贵州特色小吃推荐',
    excerpt: '带你品尝地道的贵州美食，从丝娃娃到酸汤鱼...',
    featured_image: '/images/guizhou-food.jpg',
    view_count: 1234,
    category: { name: '餐饮美食', color: '#ff6b6b' },
    slug: 'guizhou-food-recommendations'
  },
  {
    id: 2,
    title: '黄果树瀑布游玩攻略',
    excerpt: '详细的黄果树瀑布旅游指南，包含交通、住宿、景点介绍...',
    featured_image: '/images/huangguoshu.jpg',
    view_count: 2156,
    category: { name: '旅游景点', color: '#4ecdc4' },
    slug: 'huangguoshu-travel-guide'
  },
  {
    id: 3,
    title: '贵阳市最新发展动态',
    excerpt: '了解贵阳市最新的城市建设和发展规划...',
    featured_image: '/images/guiyang-news.jpg',
    view_count: 876,
    category: { name: '新闻资讯', color: '#45b7d1' },
    slug: 'guiyang-development-news'
  }
];

// 获取首页数据
const fetchHomeData = async () => {
  loading.value = true;
  try {
    const response = await axios.get('/api/home');
    const data = response.data.data || response.data;
    categories.value = data.categories || mockCategories;
    featuredArticles.value = data.featured_articles || mockArticles;
    popularArticles.value = data.popular_articles || mockArticles;
    latestArticles.value = data.latest_articles || mockArticles;
  } catch (error) {
    console.error('获取首页数据失败:', error);
    // 使用模拟数据
    categories.value = mockCategories;
    featuredArticles.value = mockArticles;
    popularArticles.value = mockArticles;
    latestArticles.value = mockArticles;
  } finally {
    loading.value = false;
  }
};

// 检查用户登录状态
const checkUserStatus = () => {
  const storedUser = localStorage.getItem('user');
  if (storedUser) {
    try {
      user.value = JSON.parse(storedUser);
    } catch (error) {
      console.error('解析用户信息失败:', error);
      localStorage.removeItem('user');
    }
  }
};

// 获取图标组件
const getIconComponent = (iconName) => {
  const iconMap = {
    'el-icon-news': Reading,
    'el-icon-food': Food,
    'el-icon-map-location': Camera,
    'el-icon-house': House,
    'el-icon-user': User,
    'el-icon-goods': Goods,
    'el-icon-chat-dot-round': ChatDotRound,
    'el-icon-service': Tools
  };
  return iconMap[iconName] || House;
};

// 跳转到登录页面
const goToLogin = () => {
  router.push('/login');
};

// 跳转到注册页面
const goToRegister = () => {
  router.push('/register');
};

// 处理用户下拉菜单命令
const handleUserCommand = async (command) => {
  switch (command) {
    case 'profile':
      // 跳转到个人中心
      ElMessage.info('个人中心功能开发中...');
      break;
    case 'admin':
      // 跳转到管理后台
      window.open('/admin/dashboard', '_blank');
      break;
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '确认退出', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        
        // 发送退出登录请求
        await axios.post('/api/auth/logout');
        
        // 清理本地存储
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user');
        
        // 清理axios默认头
        delete axios.defaults.headers.common['Authorization'];
        
        // 清理组件状态
        user.value = null;
        
        ElMessage.success('已成功退出登录');
      } catch (error) {
        if (error !== 'cancel') {
          console.error('退出登录失败:', error);
          // 即使退出登录失败，也清理本地状态
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user');
          delete axios.defaults.headers.common['Authorization'];
          user.value = null;
        }
      }
      break;
  }
};

// 跳转到分类页面
const goToCategory = (slug) => {
  console.log('跳转到分类:', slug);
};

// 跳转到文章详情
const goToArticle = (slug) => {
  console.log('跳转到文章:', slug);
};

// 处理菜单选择
const handleSelect = (key, keyPath) => {
  console.log('菜单选择:', key, keyPath);
};

// 组件挂载时获取数据
onMounted(() => {
  checkUserStatus();
  fetchHomeData();
});
</script>

<style scoped>
/* 全局样式 */
.home {
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.nav-content {
  display: flex;
  align-items: center;
  gap: 20px;
}

.logo h1 {
  margin: 0;
  font-size: 28px;
  font-weight: bold;
}

.logo span {
  font-size: 14px;
  opacity: 0.8;
}

.el-menu-demo {
  background: transparent;
  border: none;
}

.el-menu-demo .el-menu-item {
  color: white;
  border-bottom: 2px solid transparent;
}

.el-menu-demo .el-menu-item:hover,
.el-menu-demo .el-menu-item.is-active {
  background-color: rgba(255, 255, 255, 0.1);
  border-bottom-color: white;
}

.auth-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 轮播图样式 */
.carousel-item {
  height: 400px;
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel-content {
  text-align: center;
  color: white;
  background: rgba(0, 0, 0, 0.5);
  padding: 20px;
  border-radius: 10px;
}

.carousel-content h3 {
  font-size: 24px;
  margin: 0 0 10px 0;
}

.carousel-content p {
  font-size: 16px;
  margin: 0;
}

/* 功能模块样式 */
.features {
  padding: 60px 0;
  background: #f8f9fa;
}

.features h2 {
  text-align: center;
  margin-bottom: 40px;
  color: #333;
}

.feature-card {
  text-align: center;
  cursor: pointer;
  transition: transform 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  margin-bottom: 15px;
}

.feature-card h3 {
  margin: 15px 0 10px 0;
  color: #333;
}

.feature-card p {
  color: #666;
  margin: 0;
}

/* 文章样式 */
.featured-articles,
.latest-articles {
  padding: 60px 0;
}

.featured-articles h2,
.latest-articles h2 {
  text-align: center;
  margin-bottom: 40px;
  color: #333;
}

.article-card {
  cursor: pointer;
  transition: transform 0.3s;
  margin-bottom: 20px;
}

.article-card:hover {
  transform: translateY(-5px);
}

.article-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.article-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.category {
  font-size: 12px;
  font-weight: bold;
}

.views {
  color: #999;
  font-size: 12px;
}

.article-excerpt {
  color: #666;
  font-size: 14px;
  line-height: 1.6;
}

/* 页脚样式 */
.footer {
  background: #2c3e50;
  color: white;
  text-align: center;
  padding: 20px 0;
}

.footer p {
  margin: 0;
}
</style>
