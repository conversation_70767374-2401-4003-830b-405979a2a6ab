<template>
  <div class="about">
    <el-container>
      <el-header class="header">
        <div class="header-container">
          <div class="logo">
            <h1>黔进同城</h1>
            <span>贵州本地生活服务平台</span>
          </div>
          <el-button type="primary" @click="goHome">返回首页</el-button>
        </div>
      </el-header>

      <el-main>
        <div class="container">
          <el-row :gutter="20">
            <el-col :span="16">
              <el-card>
                <template #header>
                  <div class="card-header">
                    <span>关于黔进同城</span>
                  </div>
                </template>
                <div class="about-content">
                  <h2>项目简介</h2>
                  <p>
                    黔进同城是一个专注于贵州省的区域性生活服务平台，致力于为贵州本地居民和游客提供全方位的本地化服务。
                    平台整合了本地资讯、生活服务、文旅活动、美食推荐、旅游攻略、文创产品等内容，
                    同时提供用户可发布的同城信息交流功能。
                  </p>

                  <h2>核心价值</h2>
                  <ul>
                    <li><strong>本地化服务</strong>：深耕贵州市场，提供精准的本地生活服务</li>
                    <li><strong>信息聚合</strong>：整合各类生活服务信息，打造一站式服务平台</li>
                    <li><strong>社区互动</strong>：构建本地化社区，促进用户交流与互动</li>
                    <li><strong>文化传承</strong>：展示贵州独特的文化魅力和地方特色</li>
                  </ul>

                  <h2>技术栈</h2>
                  <div class="tech-tags">
                    <el-tag type="success">Laravel 12</el-tag>
                    <el-tag type="primary">Vue.js 3</el-tag>
                    <el-tag type="warning">Element Plus</el-tag>
                    <el-tag type="info">MySQL 8.0</el-tag>
                    <el-tag type="danger">Redis</el-tag>
                  </div>
                </div>
              </el-card>
            </el-col>

            <el-col :span="8">
              <el-card>
                <template #header>
                  <div class="card-header">
                    <span>联系信息</span>
                  </div>
                </template>
                <div class="contact-info">
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="项目负责人">开发团队</el-descriptions-item>
                    <el-descriptions-item label="邮箱"><EMAIL></el-descriptions-item>
                    <el-descriptions-item label="地址">贵州省贵阳市</el-descriptions-item>
                    <el-descriptions-item label="开源协议">MIT License</el-descriptions-item>
                  </el-descriptions>
                </div>
              </el-card>

              <el-card style="margin-top: 20px;">
                <template #header>
                  <div class="card-header">
                    <span>开发进度</span>
                  </div>
                </template>
                <div class="progress-info">
                  <div class="progress-item">
                    <span>项目初始化</span>
                    <el-progress :percentage="80" status="success" />
                  </div>
                  <div class="progress-item">
                    <span>前端开发</span>
                    <el-progress :percentage="60" status="warning" />
                  </div>
                  <div class="progress-item">
                    <span>后端API</span>
                    <el-progress :percentage="40" />
                  </div>
                  <div class="progress-item">
                    <span>数据库设计</span>
                    <el-progress :percentage="70" />
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';

const router = useRouter();

const goHome = () => {
  router.push('/');
};
</script>

<style scoped>
.about {
  min-height: 100vh;
  background: #f5f5f5;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.logo h1 {
  margin: 0;
  font-size: 28px;
  font-weight: bold;
}

.logo span {
  font-size: 14px;
  opacity: 0.8;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.about-content h2 {
  color: #333;
  margin-top: 20px;
  margin-bottom: 10px;
}

.about-content p {
  line-height: 1.6;
  color: #666;
  margin-bottom: 15px;
}

.about-content ul {
  line-height: 1.8;
  color: #666;
}

.about-content li {
  margin-bottom: 8px;
}

.tech-tags {
  margin-top: 10px;
}

.tech-tags .el-tag {
  margin-right: 10px;
  margin-bottom: 10px;
}

.contact-info {
  margin-bottom: 20px;
}

.progress-info {
  space-y: 15px;
}

.progress-item {
  margin-bottom: 15px;
}

.progress-item span {
  display: block;
  margin-bottom: 5px;
  color: #666;
  font-size: 14px;
}
</style>
