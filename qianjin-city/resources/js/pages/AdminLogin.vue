<template>
  <div class="admin-login-page">
    <div class="admin-login-container">
      <el-card class="admin-login-card">
        <template #header>
          <div class="card-header">
            <h2>管理后台登录</h2>
            <p>黔进同城 - 管理系统</p>
          </div>
        </template>
        
        <el-form
          ref="adminLoginFormRef"
          :model="adminLoginForm"
          :rules="adminLoginRules"
          @submit.prevent="handleAdminLogin"
        >
          <el-form-item prop="email">
            <el-input
              v-model="adminLoginForm.email"
              type="email"
              placeholder="请输入管理员邮箱"
              size="large"
              :prefix-icon="User"
            />
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="adminLoginForm.password"
              type="password"
              placeholder="请输入管理员密码"
              size="large"
              :prefix-icon="Lock"
              show-password
            />
          </el-form-item>
          
          <el-form-item>
            <el-button
              type="primary"
              size="large"
              style="width: 100%"
              :loading="loading"
              @click="handleAdminLogin"
            >
              登录管理后台
            </el-button>
          </el-form-item>
        </el-form>
        
        <div class="admin-login-footer">
          <p><router-link to="/">返回首页</router-link></p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { User, Lock } from '@element-plus/icons-vue';
import axios from 'axios';

const router = useRouter();
const adminLoginFormRef = ref();
const loading = ref(false);

const adminLoginForm = reactive({
  email: '',
  password: ''
});

const adminLoginRules = {
  email: [
    { required: true, message: '请输入管理员邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入管理员密码', trigger: 'blur' },
    { min: 6, message: '密码至少6位', trigger: 'blur' }
  ]
};

const handleAdminLogin = async () => {
  if (!adminLoginFormRef.value) return;
  
  try {
    await adminLoginFormRef.value.validate();
    loading.value = true;
    
    const response = await axios.post('/admin/login', adminLoginForm);
    
    if (response.data.success) {
      ElMessage.success(response.data.message);
      // 跳转到管理后台
      window.location.href = response.data.redirect || '/admin/dashboard';
    }
  } catch (error) {
    if (error.response && error.response.data) {
      ElMessage.error(error.response.data.message);
    } else {
      ElMessage.error('登录失败，请重试');
    }
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.admin-login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.admin-login-container {
  width: 100%;
  max-width: 400px;
}

.admin-login-card {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border: 1px solid #e0e0e0;
}

.card-header {
  text-align: center;
}

.card-header h2 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-weight: bold;
}

.card-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.admin-login-footer {
  text-align: center;
  margin-top: 20px;
}

.admin-login-footer p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.admin-login-footer a {
  color: #2c3e50;
  text-decoration: none;
}

.admin-login-footer a:hover {
  text-decoration: underline;
}
</style>
