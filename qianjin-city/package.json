{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "preview": "vite preview"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.0", "pinia": "^2.1.0", "element-plus": "^2.4.0", "@element-plus/icons-vue": "^2.3.0", "axios": "^1.8.2"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "@tailwindcss/vite": "^4.0.0", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "tailwindcss": "^4.0.0", "vite": "^6.2.4", "sass": "^1.69.0", "unplugin-vue-components": "^0.26.0", "unplugin-auto-import": "^0.17.0"}}